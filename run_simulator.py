#!/usr/bin/env python3
"""
Launcher script for the ARINC Flight Simulator.

This script runs the ARINC Flight Simulator, preferring the installed package version
but falling back to the development version if needed.

For installed package only: use run_installed_simulator.py
For development version only: run from src directory
"""
try:
    from ppbase import *
except ImportError:
    pass

import os
import sys

# Get the directory of the currently running script via ppbase.py_script_dir, available in PbaPro
try:
    local_script_dir = py_script_dir
except:
    local_script_dir = os.path.dirname(os.path.abspath(__file__))

def main():
    """Run the installed ARINC Flight Simulator package."""
    try:
        # Try to import the installed package (not from local src)
        import arinc_flight_simulator

        import os
        simulator_config_file = os.path.join(local_script_dir, "simulator_config.ini")

        # Run the simulator (will use ppbase.py_script_dir for config file if available)
        from arinc_flight_simulator.main import main as simulator_main
        try:
            simulator_main(config_file=simulator_config_file)
        except:
            print("Failed to run simulator from installed package.")
        return 


    except ImportError:
        print("ERROR: ARINC Flight Simulator package is not installed.")
        print()
        print("To install the package, run:")
        print("  pip install dist/arinc_flight_simulator-1.0.0-py3-none-any.whl")
        print()
        print("Or install from the current directory:")
        print("  pip install .")
        print()
        print("After installation, you can run the simulator with:")
        print("  arinc-simulator")
        print("  or")
        print("  python -m arinc_flight_simulator")
        return 1

if __name__ == "__main__":
    exit_code = main()
    if exit_code != 0:
        print(exit_code)
